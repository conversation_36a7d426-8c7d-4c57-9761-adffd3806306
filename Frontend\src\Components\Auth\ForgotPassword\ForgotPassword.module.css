/* ForgotPassword.module.css */
.container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 500px;
  animation: fadeInUp 0.6s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.icon {
  width: 60px;
  height: 60px;
  margin: 0 auto 20px;
  background: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.icon svg {
  width: 30px;
  height: 30px;
}

.title {
  font-size: 28px;
  font-weight: 700;
  color: #333;
  margin-bottom: 10px;
}

.subtitle {
  font-size: 16px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 0;
}

.form {
  margin-bottom: 20px;
}

.inputGroup {
  margin-bottom: 20px;
}

.label {
  display: block;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  font-size: 14px;
}

.inputWrapper {
  position: relative;
}

.inputIcon {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: #888;
  pointer-events: none;
}

.input {
  width: 100%;
  padding: 15px 15px 15px 50px;
  border: 2px solid #e1e8ed;
  border-radius: 12px;
  font-size: 16px;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.input:focus {
  border-color: #8B4513;
  outline: none;
  box-shadow: 0 0 0 3px rgba(139, 69, 19, 0.1);
}

.input:disabled {
  background-color: #f5f5f5;
  color: #999;
}

.submitBtn {
  width: 100%;
  background: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);
  color: white;
  border: none;
  padding: 16px 24px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.submitBtn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(139, 69, 19, 0.3);
}

.submitBtn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.submitBtn svg {
  width: 20px;
  height: 20px;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.error {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: white;
  padding: 15px;
  border-radius: 12px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
  font-weight: 500;
}

.error svg {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.success {
  background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
  color: white;
  padding: 15px;
  border-radius: 12px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
  font-weight: 500;
}

.success svg {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.footer {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #e1e8ed;
}

.footer p {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.link {
  color: #8B4513;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.link:hover {
  color: #A0522D;
  text-decoration: underline;
}

/* Success state styles */
.successIcon {
  width: 80px;
  height: 80px;
  margin: 0 auto 20px;
  background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.successIcon svg {
  width: 40px;
  height: 40px;
}

.infoBox {
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  color: white;
  padding: 20px;
  border-radius: 12px;
  margin: 20px 0;
  display: flex;
  gap: 15px;
  align-items: flex-start;
}

.infoIcon {
  font-size: 24px;
  flex-shrink: 0;
}

.infoBox h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
}

.infoBox p {
  margin: 4px 0;
  font-size: 14px;
  line-height: 1.4;
}

.actions {
  display: flex;
  gap: 15px;
  margin-top: 25px;
}

.backBtn, .resendBtn {
  flex: 1;
  padding: 12px 20px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  text-decoration: none;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
}

.backBtn {
  background: #f8f9fa;
  color: #666;
  border: 2px solid #e1e8ed;
}

.backBtn:hover {
  background: #e9ecef;
  color: #333;
}

.resendBtn {
  background: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);
  color: white;
}

.resendBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(139, 69, 19, 0.3);
}

/* Responsive */
@media (max-width: 768px) {
  .container {
    padding: 15px;
  }
  
  .card {
    padding: 30px 20px;
  }
  
  .title {
    font-size: 24px;
  }
  
  .actions {
    flex-direction: column;
  }
}
