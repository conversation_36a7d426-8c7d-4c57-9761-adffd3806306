<!DOCTYPE html>
<html>
<head>
    <title>Test Login API</title>
</head>
<body>
    <h1>Test Login API</h1>
    <button onclick="testLogin()">Test Login</button>
    <div id="result"></div>

    <script>
        async function testLogin() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing login...';
            
            try {
                const response = await fetch('http://localhost:5000/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        emailOrUsername: 'coffeeshop1',
                        password: 'seller123'
                    })
                });
                
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <h3>Response Status: ${response.status}</h3>
                    <h3>Response Data:</h3>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                    
                    <h3>User Role Check:</h3>
                    <p>Role: ${data.user?.role}</p>
                    <p>Shop Name: ${data.user?.shopName}</p>
                    <p>Is Seller: ${data.user?.role === 'seller'}</p>
                    <p>Is Admin: ${data.user?.role === 'admin'}</p>
                `;
                
            } catch (error) {
                resultDiv.innerHTML = `<h3>Error:</h3><pre>${error.message}</pre>`;
            }
        }
    </script>
</body>
</html>
