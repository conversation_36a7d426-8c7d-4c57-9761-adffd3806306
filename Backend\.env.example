# CoffeeHybrid Backend Environment Variables
# Copy this file to .env and fill in your actual values

# Server Configuration
PORT=5000
NODE_ENV=development

# Database Configuration
MONGODB_URI=your_mongodb_connection_string_here

# Google OAuth Configuration
# Get these from Google Cloud Console: https://console.cloud.google.com/
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here
GOOGLE_CALLBACK_URL=http://localhost:5000/api/auth/google/callback

# JWT and Session Secrets
# Generate strong random strings for these
JWT_SECRET=your_jwt_secret_here
SESSION_SECRET=your_session_secret_here

# Frontend URL (adjust based on your React dev server port)
CLIENT_URL=http://localhost:5173

# ============================================
# HOSTING CONFIGURATION
# ============================================

# ngrok Configuration (for development with HTTPS)
# Set USE_NGROK=true to enable ngrok hosting
USE_NGROK=false
NGROK_ENABLED=false
ENABLE_HTTPS=false
NGROK_AUTH_TOKEN=your_ngrok_auth_token_here
NGROK_SUBDOMAIN=your_custom_subdomain
NGROK_REGION=us
NGROK_DOMAIN=
NGROK_FRONTEND_URL=
NGROK_FRONTEND_SUBDOMAIN=

# Production Hosting URLs (auto-detected)
# These are usually set automatically by hosting platforms

# Render.com
RENDER_EXTERNAL_URL=
RENDER_SERVICE_NAME=
RENDER_SERVICE_ID=
RENDER_REGION=

# Railway
RAILWAY_STATIC_URL=
RAILWAY_SERVICE_NAME=
RAILWAY_PROJECT_ID=
RAILWAY_SERVICE_ID=

# Google Cloud
GOOGLE_CLOUD_RUN_URL=
GOOGLE_CLOUD_PROJECT=
GOOGLE_CLOUD_REGION=

# Heroku
HEROKU_APP_NAME=

# Vercel
VERCEL_URL=
VERCEL_PROJECT_ID=
VERCEL_REGION=

# Frontend URL for production
FRONTEND_URL=
