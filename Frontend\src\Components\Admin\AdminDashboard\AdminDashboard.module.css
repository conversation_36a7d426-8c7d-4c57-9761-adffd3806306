.dashboardContainer {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  min-height: 100vh;
  background-color: #f8f9fa;
}

.header {
  background: linear-gradient(135deg, #2c1810 0%, #4a2c1a 100%);
  color: white;
  padding: 2rem;
  border-radius: 12px;
  margin-bottom: 2rem;
}

.title {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
}

.shopInfo h2 {
  color: #d4a574;
  margin-bottom: 0.5rem;
}

.shopInfo p {
  opacity: 0.9;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: 1rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #d4a574;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error {
  background-color: #fee;
  color: #c33;
  padding: 1rem;
  border-radius: 6px;
  margin-bottom: 2rem;
  text-align: center;
  border: 1px solid #fcc;
}

.tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  background: white;
  padding: 0.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tab {
  padding: 1rem 1.5rem;
  border: none;
  background: transparent;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  color: #666;
}

.tab:hover {
  background-color: #f5f5f5;
  color: #333;
}

.tab.active {
  background-color: #2c1810;
  color: white;
}

.content {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dashboardStats {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.statCard {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 1.5rem;
  border-radius: 8px;
  text-align: center;
  border-left: 4px solid #d4a574;
}

.statCard h3 {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.statNumber {
  font-size: 2rem;
  font-weight: bold;
  color: #2c1810;
}

.quickActions h3 {
  color: #2c1810;
  margin-bottom: 1rem;
}

.actionButtons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.actionBtn {
  background-color: #d4a574;
  color: white;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.actionBtn:hover {
  background-color: #c19660;
}

.ordersSection {
  text-align: center;
  padding: 3rem;
  color: #666;
}

.ordersSection h3 {
  color: #2c1810;
  margin-bottom: 1rem;
}

@media (max-width: 768px) {
  .dashboardContainer {
    padding: 1rem;
  }
  
  .header {
    padding: 1.5rem;
  }
  
  .title {
    font-size: 2rem;
  }
  
  .tabs {
    flex-direction: column;
  }
  
  .tab {
    text-align: left;
  }
  
  .statsGrid {
    grid-template-columns: 1fr;
  }
  
  .actionButtons {
    flex-direction: column;
  }
}
