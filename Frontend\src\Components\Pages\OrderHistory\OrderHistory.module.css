.ordersContainer {
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem;
  min-height: 80vh;
}

.header {
  text-align: center;
  margin-bottom: 3rem;
}

.title {
  font-size: 2.5rem;
  color: #2c1810;
  margin-bottom: 0.5rem;
}

.subtitle {
  color: #666;
  font-size: 1.1rem;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: 1rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #d4a574;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: 1rem;
  text-align: center;
}

.error p {
  color: #c33;
  font-size: 1.1rem;
}

.retryBtn {
  background-color: #2c1810;
  color: white;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.retryBtn:hover {
  background-color: #3d2318;
}

.noOrders {
  text-align: center;
  padding: 4rem 2rem;
}

.noOrders h3 {
  font-size: 1.5rem;
  color: #2c1810;
  margin-bottom: 1rem;
}

.noOrders p {
  color: #666;
  margin-bottom: 2rem;
}

.browseBtn {
  background-color: #2c1810;
  color: white;
  padding: 1rem 2rem;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.browseBtn:hover {
  background-color: #3d2318;
}

.ordersList {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.orderCard {
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

.orderCard:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.orderHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.orderInfo {
  flex: 1;
}

.orderId {
  font-size: 1.2rem;
  color: #2c1810;
  margin-bottom: 0.25rem;
  font-family: monospace;
}

.orderDate {
  color: #666;
  font-size: 0.9rem;
}

.orderStatus {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
}

.statusBadge {
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: capitalize;
}

.orderTotal {
  font-size: 1.2rem;
  font-weight: bold;
  color: #2c1810;
}

.orderItems {
  margin-bottom: 1rem;
  padding: 1rem;
  background-color: #f9f9f9;
  border-radius: 6px;
}

.orderItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.orderItem:last-child {
  margin-bottom: 0;
}

.itemName {
  font-weight: 500;
  color: #2c1810;
}

.itemDetails {
  font-size: 0.9rem;
  color: #666;
  text-transform: capitalize;
}

.orderActions {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.qrBtn {
  background-color: #d4a574;
  color: white;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.qrBtn:hover {
  background-color: #c19660;
}

.cancelBtn {
  background-color: #dc3545;
  color: white;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.cancelBtn:hover {
  background-color: #c82333;
}

.expiryInfo,
.completedInfo {
  font-size: 0.9rem;
  color: #666;
  margin-left: auto;
}

.expiryWarning {
  color: #d4a574;
  font-weight: 500;
}

/* Modal Styles */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.qrModal {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  max-width: 400px;
  width: 100%;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.modalHeader h3 {
  color: #2c1810;
  margin: 0;
}

.closeBtn {
  background: none;
  border: none;
  font-size: 2rem;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qrContainer {
  background: white;
  padding: 1rem;
  border-radius: 8px;
  border: 2px solid #f0f0f0;
  display: inline-block;
  margin-bottom: 1rem;
}

.qrInstructions p {
  color: #666;
  margin-bottom: 0.5rem;
}

@media (max-width: 768px) {
  .ordersContainer {
    padding: 1rem;
  }
  
  .orderHeader {
    flex-direction: column;
    gap: 1rem;
  }
  
  .orderStatus {
    align-items: flex-start;
  }
  
  .orderActions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .qrBtn,
  .cancelBtn {
    width: 100%;
  }
  
  .expiryInfo,
  .completedInfo {
    margin-left: 0;
    text-align: center;
  }
}
