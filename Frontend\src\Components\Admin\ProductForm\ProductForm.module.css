.productForm {
  max-width: 800px;
  margin: 0 auto;
}

.productForm h3 {
  color: #2c1810;
  margin-bottom: 2rem;
  font-size: 1.5rem;
}

.error {
  background-color: #fee;
  color: #c33;
  padding: 1rem;
  border-radius: 6px;
  margin-bottom: 1rem;
  border: 1px solid #fcc;
}

.success {
  background-color: #d4edda;
  color: #155724;
  padding: 1rem;
  border-radius: 6px;
  margin-bottom: 1rem;
  border: 1px solid #c3e6cb;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.inputGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.label {
  font-weight: 600;
  color: #333;
}

.input,
.textarea {
  padding: 0.75rem;
  border: 2px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.input:focus,
.textarea:focus {
  outline: none;
  border-color: #d4a574;
}

.textarea {
  resize: vertical;
  min-height: 80px;
}

.checkboxGroup {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.checkboxLabel {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-weight: 500;
}

.checkbox {
  width: 18px;
  height: 18px;
  accent-color: #2c1810;
}

.section {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 1.5rem;
  background-color: #f9f9f9;
}

.section h4 {
  color: #2c1810;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.sizeRow {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  align-items: center;
}

.sizeInput {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.addOnRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background-color: white;
  border-radius: 4px;
  margin-bottom: 0.5rem;
}

.addOnForm {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
}

.addOnInput {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.addBtn {
  background-color: #28a745;
  color: white;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.addBtn:hover {
  background-color: #218838;
}

.removeBtn {
  background-color: #dc3545;
  color: white;
  padding: 0.25rem 0.75rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: background-color 0.3s ease;
}

.removeBtn:hover {
  background-color: #c82333;
}

.submitBtn {
  background-color: #2c1810;
  color: white;
  padding: 1rem 2rem;
  border: none;
  border-radius: 6px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
  margin-top: 1rem;
}

.submitBtn:hover:not(:disabled) {
  background-color: #3d2318;
}

.submitBtn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .row {
    grid-template-columns: 1fr;
  }
  
  .sizeRow,
  .addOnForm {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .addOnRow {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }
}
