.confirmationContainer {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
  padding: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.confirmationCard {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  max-width: 800px;
  width: 100%;
  overflow: hidden;
}

.header {
  background: linear-gradient(135deg, #2c1810 0%, #4a2c1a 100%);
  color: white;
  padding: 2rem;
  text-align: center;
}

.successIcon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.title {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
}

.subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
}

.orderDetails {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  padding: 2rem;
  border-bottom: 1px solid #eee;
}

.orderInfo h3,
.qrSection h3 {
  color: #2c1810;
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.infoRow {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.75rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #f0f0f0;
}

.orderId {
  font-family: monospace;
  background-color: #f5f5f5;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.9rem;
}

.expiryTime {
  color: #d4a574;
  font-weight: 600;
}

.total {
  font-weight: bold;
  color: #2c1810;
  font-size: 1.1rem;
}

.qrSection {
  text-align: center;
}

.qrContainer {
  background: white;
  padding: 1rem;
  border-radius: 8px;
  border: 2px solid #f0f0f0;
  display: inline-block;
  margin-bottom: 1rem;
}

.qrInstructions {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

.downloadBtn {
  background-color: #d4a574;
  color: white;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.3s ease;
}

.downloadBtn:hover {
  background-color: #c19660;
}

.orderItems {
  padding: 2rem;
  border-bottom: 1px solid #eee;
}

.orderItems h3 {
  color: #2c1810;
  margin-bottom: 1.5rem;
  font-size: 1.2rem;
}

.itemsList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.orderItem {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1rem;
  background-color: #f9f9f9;
  border-radius: 6px;
}

.itemDetails {
  flex: 1;
}

.itemName {
  color: #2c1810;
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.itemSpecs {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  font-size: 0.9rem;
  color: #666;
}

.itemSpecs span {
  text-transform: capitalize;
}

.itemPrice {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.quantity {
  font-size: 0.9rem;
  color: #666;
}

.price {
  font-weight: bold;
  color: #d4a574;
  font-size: 1.1rem;
}

.importantNote {
  padding: 2rem;
  background-color: #fff8f0;
  border-bottom: 1px solid #eee;
}

.importantNote h4 {
  color: #d4a574;
  margin-bottom: 1rem;
}

.importantNote ul {
  list-style: none;
  padding: 0;
}

.importantNote li {
  margin-bottom: 0.5rem;
  padding-left: 1rem;
  position: relative;
}

.importantNote li::before {
  content: '•';
  color: #d4a574;
  position: absolute;
  left: 0;
}

.actions {
  padding: 2rem;
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.primaryBtn {
  background-color: #2c1810;
  color: white;
  padding: 1rem 2rem;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.primaryBtn:hover {
  background-color: #3d2318;
}

.secondaryBtn {
  background-color: transparent;
  color: #2c1810;
  padding: 1rem 2rem;
  border: 2px solid #2c1810;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.secondaryBtn:hover {
  background-color: #2c1810;
  color: white;
}

@media (max-width: 768px) {
  .confirmationContainer {
    padding: 1rem;
  }
  
  .orderDetails {
    grid-template-columns: 1fr;
    padding: 1rem;
  }
  
  .orderItems,
  .importantNote,
  .actions {
    padding: 1rem;
  }
  
  .actions {
    flex-direction: column;
  }
  
  .orderItem {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .itemPrice {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
}

.tokenDisplay {
  margin-top: 15px;
  padding: 10px;
  background: #f5f5f5;
  border-radius: 8px;
  text-align: center;
}

.tokenLabel {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 5px;
}

.tokenCode {
  display: block;
  background: white;
  padding: 8px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 0.8rem;
  word-break: break-all;
  margin: 5px 0;
  border: 1px solid #ddd;
}

.copyBtn {
  background: #28a745;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  margin-top: 5px;
}

.copyBtn:hover {
  background: #218838;
}
