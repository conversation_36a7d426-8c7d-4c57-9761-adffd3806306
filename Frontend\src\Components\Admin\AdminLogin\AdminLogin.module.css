.adminLoginContainer {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background: linear-gradient(135deg, #1a1a1a 0%, #2c1810 100%);
}

.loginCard {
  background: white;
  padding: 3rem;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  width: 100%;
  max-width: 450px;
}

.title {
  font-size: 2rem;
  font-weight: bold;
  color: #2c1810;
  text-align: center;
  margin-bottom: 0.5rem;
}

.subtitle {
  color: #666;
  text-align: center;
  margin-bottom: 2rem;
}

.error {
  background-color: #fee;
  color: #c33;
  padding: 1rem;
  border-radius: 6px;
  margin-bottom: 1.5rem;
  text-align: center;
  border: 1px solid #fcc;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.inputGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.label {
  font-weight: 500;
  color: #333;
}

.input {
  padding: 0.75rem;
  border: 2px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.input:focus {
  outline: none;
  border-color: #d4a574;
}

.submitBtn {
  background-color: #2c1810;
  color: white;
  padding: 0.75rem;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
  margin-top: 1rem;
}

.submitBtn:hover:not(:disabled) {
  background-color: #3d2318;
}

.submitBtn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.footer {
  text-align: center;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #eee;
}

.footer p {
  margin-bottom: 0.5rem;
  color: #666;
}

.link {
  color: #d4a574;
  text-decoration: none;
  font-weight: 500;
}

.link:hover {
  text-decoration: underline;
}

.demoAccounts {
  margin-top: 2rem;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #d4a574;
}

.demoAccounts h4 {
  margin: 0 0 1rem 0;
  color: #2c1810;
  font-size: 0.9rem;
}

.demoAccount {
  font-size: 0.8rem;
  margin-bottom: 0.5rem;
  color: #555;
  font-family: monospace;
}

.demoAccount strong {
  color: #2c1810;
}
