.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modalContent {
  background: white;
  border-radius: 12px;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #eee;
}

.productName {
  font-size: 1.5rem;
  color: #2c1810;
  margin: 0;
}

.closeBtn {
  background: none;
  border: none;
  font-size: 2rem;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.closeBtn:hover {
  background-color: #f5f5f5;
}

.modalBody {
  padding: 1.5rem;
}

.productImage {
  width: 100%;
  height: 200px;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 1.5rem;
}

.productImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.placeholderImage {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #2c1810 0%, #4a2c1a 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.categoryIcon {
  font-size: 4rem;
  opacity: 0.7;
}

.description {
  color: #666;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.optionGroup {
  margin-bottom: 2rem;
}

.optionTitle {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c1810;
  margin-bottom: 1rem;
}

.optionButtons {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.optionBtn {
  padding: 0.5rem 1rem;
  border: 2px solid #ddd;
  background-color: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  text-transform: capitalize;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.optionBtn:hover {
  border-color: #d4a574;
}

.optionBtn.selected {
  background-color: #2c1810;
  color: white;
  border-color: #2c1810;
}

.priceModifier {
  font-size: 0.8rem;
  opacity: 0.8;
}

.addOnsList {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.addOnItem {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.addOnItem:hover {
  background-color: #f9f9f9;
}

.addOnCheckbox {
  width: 18px;
  height: 18px;
  accent-color: #2c1810;
}

.addOnName {
  flex: 1;
  font-weight: 500;
}

.addOnPrice {
  color: #d4a574;
  font-weight: 600;
}

.quantityControls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.quantityBtn {
  width: 40px;
  height: 40px;
  border: 2px solid #ddd;
  background-color: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1.2rem;
  font-weight: bold;
  transition: all 0.3s ease;
}

.quantityBtn:hover:not(:disabled) {
  border-color: #d4a574;
  background-color: #f9f9f9;
}

.quantityBtn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quantity {
  font-size: 1.2rem;
  font-weight: 600;
  min-width: 30px;
  text-align: center;
}

.modalFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-top: 1px solid #eee;
  background-color: #f9f9f9;
}

.totalPrice {
  font-size: 1.3rem;
  font-weight: bold;
  color: #2c1810;
}

.addToCartBtn {
  background-color: #2c1810;
  color: white;
  padding: 0.75rem 2rem;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.addToCartBtn:hover {
  background-color: #3d2318;
}

@media (max-width: 768px) {
  .modalContent {
    margin: 0.5rem;
    max-height: 95vh;
  }
  
  .modalHeader,
  .modalBody,
  .modalFooter {
    padding: 1rem;
  }
  
  .productImage {
    height: 150px;
  }
  
  .modalFooter {
    flex-direction: column;
    gap: 1rem;
  }
  
  .addToCartBtn {
    width: 100%;
  }
}
