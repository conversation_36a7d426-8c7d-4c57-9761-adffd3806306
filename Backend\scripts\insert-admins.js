// Script to insert 5 admin users into the database
import bcrypt from 'bcrypt';
import { User } from '../models/index.js';

export async function insertAdminUsers() {
  const admins = [
    {
      username: 'admin1',
      email: '<EMAIL>',
      password: 'adminpass1',
      firstName: 'Admin',
      lastName: 'One',
    },
    {
      username: 'admin2',
      email: '<EMAIL>',
      password: 'adminpass2',
      firstName: 'Admin',
      lastName: 'Two',
    },
    {
      username: 'admin3',
      email: '<EMAIL>',
      password: 'adminpass3',
      firstName: 'Admin',
      lastName: 'Three',
    },
    {
      username: 'admin4',
      email: '<EMAIL>',
      password: 'adminpass4',
      firstName: 'Admin',
      lastName: 'Four',
    },
    {
      username: 'admin5',
      email: '<EMAIL>',
      password: 'adminpass5',
      firstName: 'Admin',
      lastName: 'Five',
    },
  ];

  for (const admin of admins) {
    const hash = await bcrypt.hash(admin.password, 10);
    await User.findOrCreate({
      where: { email: admin.email },
      defaults: {
        ...admin,
        password: hash,
        role: 'admin',
        isEmailVerified: true,
      },
    });
  }
  console.log('✅ 5 admin users inserted (if not already present)');
}
