import swaggerJSDoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';

const options = {
    definition: {
        openapi: '3.0.0',
        info: {
            title: 'Hybrid Coffee API',
            version: '1.0.0',
            description: 'A comprehensive API for managing Hybrid Coffee, including authentication with JWT, product management, and order processing.'
        },
        components: {
            securitySchemes: {
                bearerAuth: {
                    type: 'http',
                    scheme: 'bearer',
                    bearerFormat: 'JWT'
                }
            },
            schemas: {
                User: {
                    type: 'object',
                    properties: {
                        id: { type: 'string' },
                        username: { type: 'string' },
                        email: { type: 'string' },
                        role: { type: 'string' }
                    }
                },
                Product: {
                    type: 'object',
                    properties: {
                        id: { type: 'string' },
                        name: { type: 'string' },
                        price: { type: 'number' },
                        description: { type: 'string' }
                    }
                },
                Order: {
                    type: 'object',
                    properties: {
                        id: { type: 'string' },
                        userId: { type: 'string' },
                        productId: { type: 'string' },
                        quantity: { type: 'number' },
                        status: { type: 'string' }
                    }
                }
            }
        }
    },
    apis: ['./Backend/routes/*.js', './Backend/controllers/*.js', './Backend/models/*.js']
};

export default options;