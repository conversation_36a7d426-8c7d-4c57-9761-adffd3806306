.analyticsContainer {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  background: #f8f9fa;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 2rem;
}

.title {
  color: #2c1810;
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
}

.subtitle {
  color: #666;
  font-size: 1.1rem;
}

.controls {
  display: flex;
  gap: 1rem;
  align-items: center;
  justify-content: center;
  margin-bottom: 2rem;
  padding: 1rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.controlGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.controlGroup label {
  font-weight: 600;
  color: #2c1810;
  font-size: 0.9rem;
}

.select {
  padding: 0.5rem 1rem;
  border: 2px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  background: white;
  cursor: pointer;
  transition: border-color 0.3s;
}

.select:focus {
  outline: none;
  border-color: #d4a574;
}

.refreshBtn {
  background: #d4a574;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: background-color 0.3s;
}

.refreshBtn:hover {
  background: #c19660;
}

.loading, .error {
  text-align: center;
  padding: 3rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #d4a574;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.retryBtn {
  background: #d4a574;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  margin-top: 1rem;
}

.section {
  margin-bottom: 2rem;
}

.overviewSection {
  margin-bottom: 2rem;
}

.sectionTitle {
  color: #2c1810;
  font-size: 1.8rem;
  margin-bottom: 1rem;
  text-align: center;
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.statCard {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: transform 0.3s, box-shadow 0.3s;
}

.statCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

.statIcon {
  font-size: 2.5rem;
  background: #f0f8ff;
  padding: 1rem;
  border-radius: 50%;
  min-width: 60px;
  text-align: center;
}

.statContent h3 {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.statValue {
  color: #2c1810;
  font-size: 2rem;
  font-weight: bold;
  margin: 0;
}

.tableContainer {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th {
  background: #2c1810;
  color: white;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
}

.table td {
  padding: 1rem;
  border-bottom: 1px solid #eee;
}

.table tr:hover {
  background: #f8f9fa;
}

.rank {
  font-weight: bold;
  color: #d4a574;
  position: relative;
}

.crown {
  position: absolute;
  top: -5px;
  right: -5px;
  font-size: 0.8rem;
}

.productName {
  font-weight: 600;
  color: #2c1810;
}

.category {
  background: #f0f8ff;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  text-transform: capitalize;
}

.number {
  font-weight: 600;
  color: #333;
}

.price {
  font-weight: 600;
  color: #d4a574;
}

.categoryGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.categoryCard {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: transform 0.3s;
}

.categoryCard:hover {
  transform: translateY(-2px);
}

.categoryHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #f0f8ff;
}

.categoryHeader h3 {
  color: #2c1810;
  margin: 0;
  text-transform: capitalize;
}

.categoryRank {
  background: #d4a574;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: bold;
}

.categoryStats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
}

.categoryStat {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.categoryStat .label {
  color: #666;
  font-size: 0.9rem;
}

.categoryStat .value {
  font-weight: 600;
  color: #2c1810;
}

.detailsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.detailCard {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.detailCard h3 {
  color: #2c1810;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #f0f8ff;
}

.detailList {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.detailItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 6px;
}

.detailLabel {
  font-weight: 600;
  color: #2c1810;
  text-transform: capitalize;
}

.detailStats {
  display: flex;
  gap: 1rem;
  font-size: 0.9rem;
  color: #666;
}

.exportSection {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  text-align: center;
  margin-top: 2rem;
}

.exportSection h3 {
  color: #2c1810;
  margin-bottom: 1rem;
}

.exportButtons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.exportBtn {
  background: #d4a574;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: background-color 0.3s;
}

.exportBtn:hover:not(:disabled) {
  background: #c19660;
}

.exportBtn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .analyticsContainer {
    padding: 1rem;
  }
  
  .controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .statsGrid {
    grid-template-columns: 1fr;
  }
  
  .table {
    font-size: 0.8rem;
  }
  
  .table th,
  .table td {
    padding: 0.5rem;
  }
  
  .categoryGrid,
  .detailsGrid {
    grid-template-columns: 1fr;
  }
  
  .exportButtons {
    flex-direction: column;
  }
}
