// const nodemailer = require('nodemailer');
// require('dotenv').config(); // Load environment variables
// const crypto = require('crypto'); // For token generation

// // Step 1: Create transporter (using environment variables)
// const transporter = nodemailer.createTransport({
//   service: 'gmail',
//   auth: {
//     user: process.env.GMAIL_USER, // '<EMAIL>' loaded from .env
//     pass: process.env.GMAIL_APP_PASSWORD // App Password loaded from .env
//   }
// });

// // Generate a token (for example, a random string)
// const token = crypto.randomBytes(20).toString('hex');

// // Step 2: Email options
// const mailOptions = {
//   from: '"Choeng Rayu" <<EMAIL>>',
//   to: '<EMAIL>',
//   subject: 'Test Email from Node.js',
//   text: 'This is a plain text email! Your confirmation token: ' + token,
//   html: `
//     <b>This is HTML content!</b><br/>
//     <a href="https://yourdomain.com/confirm?token=${token}" style="display:inline-block;padding:10px 20px;background:#4CAF50;color:#fff;text-decoration:none;border-radius:5px;">Confirm with Token</a>
//     <p>If the button does not work, use this token: <b>${token}</b></p>
//   `
// };

// // Step 3: Send email (async/await recommended)
// (async () => {
//   try {
//     const info = await transporter.sendMail(mailOptions);
//     console.log('Email sent:', info.response);
//   } catch (error) {
//     console.error('Error:', error);
//   }
// })();















import { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useUser } from '../../../context/UserContext';
import { authAPI } from '../../../services/api';
import styles from './Login.module.css';
import SendingEmail from '../SendingEmail';

const Login = () => {
  const [formData, setFormData] = useState({
    emailOrUsername: '',
    password: ''
  });
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);

  const { login } = useUser();
  const navigate = useNavigate();
  const location = useLocation();

//   // Handle Google OAuth callback
//   useEffect(() => {
//     const urlParams = new URLSearchParams(location.search);
//     const token = urlParams.get('token');
//     const userStr = urlParams.get('user');
//     const error = urlParams.get('error');

//     if (error) {
//       if (error === 'oauth_not_configured') {
//         setError('Google OAuth is not configured. Please use email/password login.');
//       } else {
//         setError('Google authentication failed. Please try again.');
//       }
//       return;
//     }

//     if (token && userStr) {
//       try {
//         const user = JSON.parse(decodeURIComponent(userStr));

//         // Store token and user data
//         localStorage.setItem('token', token);
//         localStorage.setItem('user', JSON.stringify(user));

//         // Login user
//         login(user);

//         // Redirect based on role
//         if (user.role === 'admin' || user.role === 'seller') {
//           navigate('/seller-dashboard');
//         } else {
//           navigate('/menu');
//         }
//       } catch (error) {
//         console.error('Error parsing user data:', error);
//         setError('Authentication error. Please try again.');
//       }
//     }
//   }, [location.search, login, navigate]);

//   const handleGoogleLogin = () => {
//     setIsGoogleLoading(true);
//     setError('');

//     // Redirect to Google OAuth
//     const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
//     window.location.href = `${API_BASE_URL}/auth/google`;
//   };

//   const handleChange = (e) => {
//     setFormData({
//       ...formData,
//       [e.target.name]: e.target.value
//     });
//     setError(''); // Clear error when user types
//   };

//   const handleSubmit = async (e) => {
//     e.preventDefault();
//     setIsLoading(true);
//     setError('');

//     try {
//       const response = await authAPI.login(formData);
//       login(response.user);
//       navigate('/menu');
//     } catch (error) {
//       setError(error.response?.data?.error || 'Login failed. Please try again.');
//     } finally {
//       setIsLoading(false);
//     }
//   };

  return (
    <div className={styles.loginContainer}>
      <div className={styles.loginCard}>
        <h2 className={styles.title}>Welcome Back</h2>
        <p className={styles.subtitle}>Sign in to your account</p>

        {error && (
          <div className={styles.error}>
            {error}
          </div>
        )}

        {/* Google OAuth Button */}
        <button
          type="button"
          onClick={handleGoogleLogin}
          className={styles.googleBtn}
          disabled={isGoogleLoading || isLoading}
        >
          <svg className={styles.googleIcon} viewBox="0 0 24 24">
            <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
            <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
            <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
            <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
          </svg>
          {isGoogleLoading ? 'Connecting to Google...' : 'Continue with Google'}
        </button>

        <div className={styles.divider}>
          <span>or</span>
        </div>

        <form onSubmit={handleSubmit} className={styles.form}>
          <div className={styles.inputGroup}>
            <label htmlFor="emailOrUsername" className={styles.label}>
              Email or Username
            </label>
            <input
              type="text"
              id="emailOrUsername"
              name="emailOrUsername"
              value={formData.emailOrUsername}
              onChange={handleChange}
              className={styles.input}
              required
              placeholder="Enter your email or username"
            />
          </div>

          <div className={styles.inputGroup}>
            <label htmlFor="password" className={styles.label}>
              Password
            </label>
            <input
              type="password"
              id="password"
              name="password"
              value={formData.password}
              onChange={handleChange}
              className={styles.input}
              required
              placeholder="Enter your password"
            />
          </div>

          <button
            type="submit"
            className={styles.submitBtn}
            disabled={isLoading} // use for disabling the button while loading
          >
            {isLoading ? 'Signing In...' : 'Sign In'} // use for showing loading text or default text
          </button>
        </form>

        <div className={styles.footer}>
          <p>
            Don't have an account?{' '}
            <Link to="/register" className={styles.link}> // use for navigating to the registration page
              Sign up here
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default Login;
