.scannerContainer {
  min-height: 100vh;
  background: linear-gradient(135deg, #2c1810 0%, #4a2c1a 100%);
  padding: 1rem;
  display: flex;
  align-items: flex-start;
  justify-content: center;
}

.scannerCard {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  max-width: 1000px;
  width: 100%;
  overflow: hidden;
  margin-top: 2rem;
}

.header {
  background: linear-gradient(135deg, #d4a574 0%, #b8935f 100%);
  color: #2c1810;
  padding: 3rem 2rem;
  text-align: center;
}

.title {
  font-size: 3rem;
  margin-bottom: 1rem;
  font-weight: bold;
}

.subtitle {
  font-size: 1.2rem;
  opacity: 0.8;
  font-weight: 500;
}

.modeSelection {
  display: flex;
  padding: 2rem;
  gap: 1rem;
  background: #f8f9fa;
  border-bottom: 1px solid #eee;
}

.modeBtn {
  flex: 1;
  padding: 1.5rem;
  border: 3px solid #ddd;
  background: white;
  border-radius: 12px;
  cursor: pointer;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.modeBtn:hover {
  border-color: #d4a574;
  transform: translateY(-2px);
}

.modeBtn.active {
  border-color: #2c1810;
  background-color: #2c1810;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(44, 24, 16, 0.3);
}

.cameraSection {
  padding: 3rem;
  background: #f8f9fa;
}

.cameraStart {
  text-align: center;
  padding: 4rem 2rem;
}

.cameraIcon {
  font-size: 5rem;
  margin-bottom: 2rem;
  opacity: 0.7;
}

.cameraStart h3 {
  color: #2c1810;
  margin-bottom: 1rem;
  font-size: 1.8rem;
}

.cameraStart p {
  color: #666;
  margin-bottom: 3rem;
  font-size: 1.1rem;
}

.startCameraBtn {
  background: linear-gradient(135deg, #2c1810 0%, #4a2c1a 100%);
  color: white;
  padding: 1.5rem 3rem;
  border: none;
  border-radius: 12px;
  font-size: 1.2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(44, 24, 16, 0.3);
}

.startCameraBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(44, 24, 16, 0.4);
}

.cameraActive {
  text-align: center;
}

.videoContainer {
  position: relative;
  display: inline-block;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
  margin-bottom: 2rem;
}

.video {
  width: 100%;
  max-width: 600px;
  height: auto;
  display: block;
}

.scanOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  pointer-events: none;
}

.scanFrame {
  width: 250px;
  height: 250px;
  border: 4px solid #d4a574;
  border-radius: 16px;
  position: relative;
  animation: pulse 2s infinite;
  box-shadow: 0 0 20px rgba(212, 165, 116, 0.5);
}

.scanFrame::before,
.scanFrame::after {
  content: '';
  position: absolute;
  width: 30px;
  height: 30px;
  border: 4px solid #d4a574;
}

.scanFrame::before {
  top: -4px;
  left: -4px;
  border-right: none;
  border-bottom: none;
}

.scanFrame::after {
  bottom: -4px;
  right: -4px;
  border-left: none;
  border-top: none;
}

@keyframes pulse {
  0%, 100% { 
    opacity: 1; 
    transform: scale(1);
  }
  50% { 
    opacity: 0.7; 
    transform: scale(1.05);
  }
}

.scanInstructions {
  color: white;
  background: rgba(0, 0, 0, 0.8);
  padding: 1rem 2rem;
  border-radius: 25px;
  margin-top: 2rem;
  font-weight: 600;
  font-size: 1.1rem;
}

.cameraControls {
  margin-top: 2rem;
}

.stopCameraBtn {
  background-color: #dc3545;
  color: white;
  padding: 1rem 2rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.stopCameraBtn:hover {
  background-color: #c82333;
  transform: translateY(-1px);
}

.manualSection {
  padding: 3rem;
  background: white;
}

.manualForm {
  max-width: 600px;
  margin: 0 auto;
}

.inputGroup {
  margin-bottom: 2rem;
}

.label {
  display: block;
  font-weight: 600;
  color: #2c1810;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.input {
  width: 100%;
  padding: 1.5rem;
  border: 3px solid #ddd;
  border-radius: 12px;
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.input:focus {
  outline: none;
  border-color: #d4a574;
  box-shadow: 0 0 0 3px rgba(212, 165, 116, 0.1);
}

.buttonGroup {
  display: flex;
  gap: 1rem;
}

.verifyBtn {
  flex: 1;
  background: linear-gradient(135deg, #2c1810 0%, #4a2c1a 100%);
  color: white;
  padding: 1.5rem;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.verifyBtn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(44, 24, 16, 0.3);
}

.verifyBtn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

.resetBtn {
  background-color: #6c757d;
  color: white;
  padding: 1.5rem 2rem;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.resetBtn:hover {
  background-color: #5a6268;
  transform: translateY(-1px);
}

.errorResult,
.successResult {
  margin: 2rem;
  padding: 3rem;
  border-radius: 16px;
  text-align: center;
}

.errorResult {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  border: 2px solid #f5c6cb;
  color: #721c24;
}

.successResult {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  border: 2px solid #c3e6cb;
  color: #155724;
}

.resultIcon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.orderSummary {
  text-align: left;
  margin: 2rem 0;
  background: white;
  padding: 2rem;
  border-radius: 12px;
  border: 2px solid #c3e6cb;
}

.customerInfo h4 {
  color: #2c1810;
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.orderItems {
  margin-top: 1.5rem;
}

.orderItems h4 {
  color: #2c1810;
  margin-bottom: 1rem;
}

.orderItem {
  display: flex;
  justify-content: space-between;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 0.5rem;
}

.itemName {
  font-weight: 600;
  color: #2c1810;
}

.itemDetails {
  color: #666;
  font-size: 0.9rem;
}

.newOrderBtn {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  padding: 1.5rem 3rem;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  font-size: 1.2rem;
  margin-top: 2rem;
  transition: all 0.3s ease;
}

.newOrderBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.recentScans {
  margin: 2rem;
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 12px;
}

.recentScans h4 {
  color: #2c1810;
  margin-bottom: 1rem;
}

.scanItem {
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 0.5rem;
  border-left: 4px solid;
}

.scanItem.success {
  background: #d4edda;
  border-left-color: #28a745;
}

.scanItem.failed {
  background: #f8d7da;
  border-left-color: #dc3545;
}

.scanInfo {
  display: flex;
  justify-content: space-between;
  font-weight: 600;
}

.scanDetails {
  margin-top: 0.5rem;
  color: #666;
  font-size: 0.9rem;
}

.instructions {
  background: #2c1810;
  color: white;
  padding: 3rem;
}

.instructions h4 {
  color: #d4a574;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
}

.instructions ol {
  padding-left: 1.5rem;
}

.instructions li {
  padding: 0.5rem 0;
  font-size: 1.1rem;
  line-height: 1.6;
}

.dismissBtn {
  background-color: #dc3545;
  color: white;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  margin-top: 1rem;
  font-weight: 600;
}

@media (max-width: 768px) {
  .scannerContainer {
    padding: 0.5rem;
  }
  
  .header {
    padding: 2rem 1rem;
  }
  
  .title {
    font-size: 2rem;
  }
  
  .modeSelection {
    flex-direction: column;
    padding: 1rem;
  }
  
  .buttonGroup {
    flex-direction: column;
  }
  
  .orderSummary {
    padding: 1rem;
  }
}
