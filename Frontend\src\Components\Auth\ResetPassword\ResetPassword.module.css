/* ResetPassword.module.css */
.container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #8B4513 0%, #D2691E 50%, #CD853F 100%);
  padding: 20px;
  position: relative;
  overflow: hidden;
}

.container::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
  animation: float 25s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-30px) rotate(180deg); }
}

.card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border-radius: 20px;
  box-shadow: 0 25px 70px rgba(0, 0, 0, 0.2);
  padding: 40px;
  width: 100%;
  max-width: 520px;
  animation: fadeInUp 0.8s ease;
  position: relative;
  z-index: 1;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.icon {
  width: 60px;
  height: 60px;
  margin: 0 auto 20px;
  background: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.icon svg {
  width: 30px;
  height: 30px;
}

.successIcon {
  width: 80px;
  height: 80px;
  margin: 0 auto 20px;
  background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.successIcon svg {
  width: 40px;
  height: 40px;
}

.errorIcon {
  width: 80px;
  height: 80px;
  margin: 0 auto 20px;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.errorIcon svg {
  width: 40px;
  height: 40px;
}

.title {
  font-size: 32px;
  font-weight: 700;
  background: linear-gradient(135deg, #8B4513 0%, #D2691E 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 10px;
  position: relative;
}

.title::after {
  content: '🔑';
  position: absolute;
  top: -5px;
  right: -35px;
  font-size: 1.2rem;
  animation: rotate 3s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.subtitle {
  font-size: 16px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 0;
}

.form {
  margin-bottom: 20px;
}

.inputGroup {
  margin-bottom: 20px;
}

.label {
  display: block;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  font-size: 14px;
}

.inputWrapper {
  position: relative;
}

.inputIcon {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: #888;
  pointer-events: none;
}

.input {
  width: 100%;
  padding: 15px 15px 15px 50px;
  border: 2px solid #e1e8ed;
  border-radius: 12px;
  font-size: 16px;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.input:focus {
  border-color: #8B4513;
  outline: none;
  box-shadow: 0 0 0 3px rgba(139, 69, 19, 0.1);
}

.input:disabled {
  background-color: #f5f5f5;
  color: #999;
}

.passwordStrength {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 20px;
}

.passwordStrength h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #333;
  font-weight: 600;
}

.passwordStrength ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.passwordStrength li {
  padding: 4px 0;
  font-size: 14px;
  color: #666;
  position: relative;
  padding-left: 25px;
}

.passwordStrength li::before {
  content: '✗';
  position: absolute;
  left: 0;
  color: #dc3545;
  font-weight: bold;
}

.passwordStrength li.valid::before {
  content: '✓';
  color: #28a745;
}

.submitBtn {
  width: 100%;
  background: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);
  color: white;
  border: none;
  padding: 16px 24px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.submitBtn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(139, 69, 19, 0.3);
}

.submitBtn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.submitBtn svg {
  width: 20px;
  height: 20px;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.error {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: white;
  padding: 15px;
  border-radius: 12px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
  font-weight: 500;
}

.error svg {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.footer {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #e1e8ed;
}

.footer p {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.link {
  color: #8B4513;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.link:hover {
  color: #A0522D;
  text-decoration: underline;
}

.actions {
  display: flex;
  gap: 15px;
  margin-top: 25px;
}

.loginBtn, .requestBtn, .backBtn {
  flex: 1;
  padding: 12px 20px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  text-decoration: none;
  text-align: center;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.loginBtn {
  background: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);
  color: white;
}

.loginBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(139, 69, 19, 0.3);
}

.loginBtn svg {
  width: 18px;
  height: 18px;
}

.requestBtn {
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  color: white;
}

.requestBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(116, 185, 255, 0.3);
}

.backBtn {
  background: #f8f9fa;
  color: #666;
  border: 2px solid #e1e8ed;
}

.backBtn:hover {
  background: #e9ecef;
  color: #333;
}

/* Responsive */
@media (max-width: 768px) {
  .container {
    padding: 15px;
  }
  
  .card {
    padding: 30px 20px;
  }
  
  .title {
    font-size: 24px;
  }
  
  .actions {
    flex-direction: column;
  }
}
