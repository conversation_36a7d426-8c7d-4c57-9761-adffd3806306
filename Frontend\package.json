{"name": "coffeehybrid", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "server": "cd Backend && npm run dev", "bot": "cd Bot && npm run start", "start": "node server.js", "start:backend": "cd Backend && npm start", "install:backend": "cd Backend && npm install", "deploy": "npm run install:backend"}, "dependencies": {"axios": "^1.6.0", "axois": "^0.0.1-security", "jsqr": "^1.4.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-qr-code": "^2.0.12", "react-router-dom": "^6.20.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.5"}}