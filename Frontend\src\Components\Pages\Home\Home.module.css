.homeContainer {
  min-height: 100vh;
}

.hero {
  background: linear-gradient(135deg, #2c1810 0%, #a79990 100%);
  color: white;
  padding: 4rem 2rem;
  text-align: center;
}

.heroContent {
  max-width: 800px;
  margin: 0 auto;
}

.heroTitle {
  font-size: 3.5rem;
  font-weight: bold;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, #d4a574, #e6b885);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.heroSubtitle {
  font-size: 1.2rem;
  margin-bottom: 3rem;
  opacity: 0.9;
  line-height: 1.6;
}

.welcomeBack h2 {
  font-size: 2rem;
  margin-bottom: 2rem;
  color: #d4a574;
}

.authPrompt {
  margin-top: 2rem;
}

.actionButtons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.primaryBtn {
  background-color: #d4a574;
  color: #2c1810;
  padding: 1rem 2rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: bold;
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.primaryBtn:hover {
  background-color: #e6b885;
  transform: translateY(-2px);
}

.secondaryBtn {
  background-color: transparent;
  color: white;
  padding: 1rem 2rem;
  border: 2px solid #d4a574;
  border-radius: 8px;
  text-decoration: none;
  font-weight: bold;
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.secondaryBtn:hover {
  background-color: #d4a574;
  color: #2c1810;
}

.features {
  padding: 4rem 2rem;
  background-color: #f8f9fa;
}

.featuresContainer {
  max-width: 1200px;
  margin: 0 auto;
}

.sectionTitle {
  font-size: 2.5rem;
  text-align: center;
  margin-bottom: 3rem;
  color: #2c1810;
}

.featureGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.featureCard {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.featureCard:hover {
  transform: translateY(-5px);
}

.featureIcon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.featureCard h3 {
  font-size: 1.3rem;
  margin-bottom: 1rem;
  color: #2c1810;
}

.featureCard p {
  color: #666;
  line-height: 1.6;
}

.categories {
  padding: 4rem 2rem;
  background-color: white;
}

.categoriesContainer {
  max-width: 1000px;
  margin: 0 auto;
}

.categoryGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.categoryCard {
  background: linear-gradient(135deg, #2c1810 0%, #4a2c1a 100%);
  color: white;
  padding: 2.5rem;
  border-radius: 12px;
  text-align: center;
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.categoryCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.categoryIcon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.categoryCard h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #d4a574;
}

.categoryCard p {
  opacity: 0.9;
  line-height: 1.6;
}

@media (max-width: 768px) {
  .heroTitle {
    font-size: 2.5rem;
  }
  
  .heroSubtitle {
    font-size: 1rem;
  }
  
  .actionButtons {
    flex-direction: column;
    align-items: center;
  }
  
  .primaryBtn,
  .secondaryBtn {
    width: 200px;
  }
  
  .featureGrid,
  .categoryGrid {
    grid-template-columns: 1fr;
  }
}
