services:
  # Backend API Service
  - type: web
    name: coffeehybrid-api
    env: node
    plan: free
    buildCommand: cd Backend && npm install
    startCommand: cd Backend && npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 10000
      - key: MON<PERSON>ODB_URI
        sync: false
      - key: GOO<PERSON>LE_CLIENT_ID
        sync: false
      - key: GOOGLE_CLIENT_SECRET
        sync: false
      - key: JWT_SECRET
        sync: false
      - key: SESSION_SECRET
        sync: false
      - key: CLIENT_URL
        value: https://coffeehybrid-frontend.onrender.com
      - key: FRONTEND_URL
        value: https://coffeehybrid-frontend.onrender.com

  # Frontend Static Site
  - type: static
    name: coffeehybrid-frontend
    buildCommand: npm install && npm run build
    staticPublishPath: ./dist
    envVars:
      - key: VITE_API_URL
        value: https://coffeehybrid-api.onrender.com/api
